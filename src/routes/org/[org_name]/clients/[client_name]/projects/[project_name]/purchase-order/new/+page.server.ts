import { fail } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { purchaseOrderSchema } from '$lib/schemas/purchase_order';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser, requireProject } from '$lib/server/auth';
import { createVendor } from '$lib/components/forms/vendor/vendor_form_actions';
import { vendorSchema } from '$lib/schemas/vendor';

export const load: PageServerLoad = async ({ params, locals, cookies }) => {
	const { user } = await requireUser();

	const { supabase } = locals;
	const { org_name, client_name, project_name } = requireProject();

	// Fetch the project data to get project_id
	const { data: projectData, error: projectError } = await supabase
		.from('project')
		.select('project_id, name, client!inner(name, client_id, organization(name, org_id))')
		.eq('client.organization.name', org_name)
		.eq('client.name', client_name)
		.eq('name', project_name)
		.limit(1)
		.maybeSingle();

	if (projectError || !projectData) {
		console.error('Error fetching project:', projectError);
		throw new Error('Project not found');
	}

	// Fetch accessible vendors for this project
	const { data: vendors, error: vendorsError } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: user.id,
		entity_type_param: 'project',
		entity_id_param: projectData.project_id,
	});

	if (vendorsError) {
		console.error('Error fetching vendors:', vendorsError);
		throw new Error('Failed to fetch vendors');
	}

	const form = await superValidate(zod(purchaseOrderSchema));
	const newVendorForm = await superValidate(zod(vendorSchema));

	return {
		form,
		newVendorForm,
		project: projectData,
		vendors: vendors || [],
	};
};

export const actions: Actions = {
	createVendor,
	createPurchaseOrder: async ({ request, locals, cookies, params }) => {
		const { user } = await requireUser();
		const { supabase } = locals;
		const { org_name, client_name, project_name } = requireProject();

		const form = await superValidate(request, zod(purchaseOrderSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Get project_id
		const { data: projectData, error: projectError } = await supabase
			.from('project')
			.select('*, client!inner(name, organization(name, org_id))')
			.eq('client.organization.name', org_name)
			.eq('client.name', client_name)
			.eq('name', project_name)
			.limit(1)
			.maybeSingle();

		if (projectError || !projectData) {
			console.error('Error fetching project:', projectError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Project not found' },
			});
		}

		// Prepare the purchase order data
		const purchaseOrderData = {
			...form.data,
			project_id: projectData.project_id,
			created_by_user_id: user.id,
		};

		// Insert the purchase order
		const { data: purchaseOrder, error: purchaseOrderError } = await supabase
			.from('purchase_order')
			.insert(purchaseOrderData)
			.select('purchase_order_id, po_number')
			.single();

		if (purchaseOrderError) {
			console.error('Error creating purchase order:', purchaseOrderError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to create purchase order' },
			});
		}

		return redirect(
			`/org/${encodeURIComponent(org_name)}/clients/${encodeURIComponent(client_name)}/projects/${encodeURIComponent(project_name)}/purchase-order`,
			{
				type: 'success',
				message: `Purchase order ${purchaseOrder.po_number} created successfully`,
			},
			cookies,
		);
	},
};
